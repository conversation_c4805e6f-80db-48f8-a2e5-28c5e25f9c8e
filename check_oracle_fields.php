<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 检查Oracle查询返回的字段 ===" . PHP_EOL;

try {
    $sql = "SELECT * FROM (
        SELECT a.*, ROWNUM rnum FROM (
            SELECT * FROM BMBA_T WHERE BMBAENT = 40 ORDER BY BMBAENT, BMBASITE, BMBA001, BMBA003, BMBA004
        ) a WHERE ROWNUM <= 1
    ) WHERE rnum > 0";
    
    $result = DB::connection('oracle')->select($sql);
    
    if (!empty($result)) {
        $record = (array) $result[0];
        echo "字段列表:" . PHP_EOL;
        foreach (array_keys($record) as $field) {
            echo "  " . $field . " (类型: " . gettype($record[$field]) . ")" . PHP_EOL;
        }
        
        echo PHP_EOL . "检查是否包含RN相关字段:" . PHP_EOL;
        $rnFields = [];
        foreach (array_keys($record) as $field) {
            if (stripos($field, 'rn') !== false || stripos($field, 'rownum') !== false) {
                $rnFields[] = $field;
            }
        }
        
        if (!empty($rnFields)) {
            echo "发现RN相关字段: " . implode(', ', $rnFields) . PHP_EOL;
        } else {
            echo "未发现RN相关字段" . PHP_EOL;
        }
        
        // 测试字段移除
        echo PHP_EOL . "测试字段移除:" . PHP_EOL;
        unset($record['rnum']);
        unset($record['RNUM']);
        unset($record['rn']);
        unset($record['RN']);
        
        echo "移除后剩余字段数: " . count($record) . PHP_EOL;
        
        $stillHasRN = false;
        foreach (array_keys($record) as $field) {
            if (stripos($field, 'rn') !== false || stripos($field, 'rownum') !== false) {
                echo "⚠️  仍然存在RN相关字段: " . $field . PHP_EOL;
                $stillHasRN = true;
            }
        }
        
        if (!$stillHasRN) {
            echo "✅ 所有RN相关字段已移除" . PHP_EOL;
        }
        
    } else {
        echo "未获取到数据" . PHP_EOL;
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== 检查完成 ===" . PHP_EOL;
