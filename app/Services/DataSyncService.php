<?php

declare(strict_types=1);

namespace App\Services;

use App\Jobs\SyncTableJob;
use App\Jobs\TransformDataJob;
use App\Models\SyncLog;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class DataSyncService
{
    /**
     * 获取同步表配置
     */
    private function getSyncTables(): array
    {
        $tables = config('sync.tables', []);
        
        // 过滤启用的表
        return array_filter($tables, function ($config) {
            return $config['enabled'] ?? true;
        });
    }

    /**
     * 获取表与数据转化类型的映射关系
     */
    private function getTableTransformMapping(): array
    {
        return [
            'RTAXL_T' => ['category'],           // 分类数据
            'PMAB_T' => ['customer'],            // 客户主表
            'PMAAL_T' => ['customer'],           // 客户多语言表
            'BMBA_T' => ['bom'],                 // BOM表
            'IMAA_T' => ['material'],            // 物料主表
            'IMAAL_T' => ['material'],           // 物料多语言表
            'IMAF_T' => ['material'],            // 物料补给策略表
            'BMAA_T' => ['material']             // 物料BOM主表
        ];
    }

    /**
     * 检查是否需要自动触发数据转化
     */
    private function shouldAutoTransform(): bool
    {
        return config('sync.auto_transform.enabled', true);
    }

    private DatabaseService $databaseService;

    public function __construct(DatabaseService $databaseService)
    {
        $this->databaseService = $databaseService;
    }

    /**
     * 同步所有表
     */
    public function syncAllTables(string $syncType = SyncLog::TYPE_INCREMENTAL): array
    {
        $results = [];
        $syncTables = $this->getSyncTables();
        
        foreach ($syncTables as $tableName => $config) {
            try {
                $result = $this->syncTable($tableName, $syncType, $config);
                $results[$tableName] = $result;
            } catch (Exception $e) {
                Log::error("同步表 {$tableName} 失败", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                $results[$tableName] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }

    /**
     * 并发同步多个表（使用Laravel队列实现真正的并发）
     */
    public function syncTablesParallel(array $tableNames, string $syncType = SyncLog::TYPE_INCREMENTAL): array
    {
        $syncTables = $this->getSyncTables();
        $validTables = [];
        $invalidTables = [];
        $runningTables = [];

        // 首先检查是否有任何表正在运行（全局检查）
        $globalRunningTasks = SyncLog::where('status', SyncLog::STATUS_RUNNING)
            ->where('created_at', '>', Carbon::now()->subHours(2))
            ->get(['table_name', 'start_time', 'sync_type']);

        if ($globalRunningTasks->isNotEmpty()) {
            $runningTableNames = $globalRunningTasks->pluck('table_name')->unique()->toArray();
            
            // 如果有全局运行的任务，检查是否与请求的表有冲突
            $conflictTables = array_intersect($tableNames, $runningTableNames);
            
            if (!empty($conflictTables)) {
                // 如果请求的表中有正在运行的，返回错误
                $results = [];
                foreach ($tableNames as $tableName) {
                    if (in_array($tableName, $conflictTables)) {
                        $runningTask = $globalRunningTasks->where('table_name', $tableName)->first();
                        $results[$tableName] = [
                            'success' => false,
                            'error' => "表 {$tableName} 已有同步任务在运行中，开始时间: " . 
                                     $runningTask->start_time->format('Y-m-d H:i:s') . 
                                     "，类型: " . ($runningTask->sync_type === 'full' ? '全量同步' : '增量同步')
                        ];
                    } else {
                        $results[$tableName] = [
                            'success' => false,
                            'error' => "系统中有其他表正在同步，为避免资源冲突暂时无法启动新的同步任务。正在运行的表: " . 
                                     implode(', ', $runningTableNames)
                        ];
                    }
                }
                return $results;
            }
        }

        // 验证表名是否有效
        foreach ($tableNames as $tableName) {
            if (!isset($syncTables[$tableName])) {
                $invalidTables[] = $tableName;
                continue;
            }

            // 再次检查单表状态（双重检查）
            $runningSync = SyncLog::where('table_name', $tableName)
                ->where('status', SyncLog::STATUS_RUNNING)
                ->where('created_at', '>', Carbon::now()->subHours(2))
                ->first();

            if ($runningSync) {
                $runningTables[] = $tableName;
            } else {
                $validTables[] = $tableName;
            }
        }

        // 如果没有有效的表
        if (empty($validTables)) {
            $results = [];
            
            // 为无效表添加错误结果
            foreach ($invalidTables as $tableName) {
                $results[$tableName] = [
                    'success' => false,
                    'error' => "表 {$tableName} 没有有效的同步配置"
                ];
            }
            
            // 为正在运行的表添加错误结果
            foreach ($runningTables as $tableName) {
                $results[$tableName] = [
                    'success' => false,
                    'error' => "表 {$tableName} 已有同步任务在运行中"
                ];
            }
            
            return $results;
        }

        // 检查队列系统是否正常运行
        if (count($validTables) > 1) {
            $queueStatus = $this->checkQueueStatus();
            if (!$queueStatus['running']) {
                // 队列未运行，改为顺序执行
                Log::warning("队列工作进程未运行，改为顺序执行", [
                    'tables' => $validTables,
                    'queue_status' => $queueStatus
                ]);
                
                return $this->syncTablesSequentially($validTables, $syncType, $syncTables, $invalidTables, $runningTables);
            }
        }

        // 如果只有一个表，直接同步（不使用队列）
        if (count($validTables) === 1) {
            $tableName = $validTables[0];
            try {
                $result = $this->syncTable($tableName, $syncType, $syncTables[$tableName]);
                return [$tableName => $result];
            } catch (Exception $e) {
                return [$tableName => [
                    'success' => false,
                    'error' => $e->getMessage()
                ]];
            }
        }

        // 多表并发同步：使用队列批处理
        return $this->syncTablesWithQueue($validTables, $syncType, $syncTables, $invalidTables, $runningTables);
    }

    /**
     * 使用队列系统实现真正的并发同步
     */
    private function syncTablesWithQueue(array $tableNames, string $syncType, array $syncTables, array $invalidTables = [], array $runningTables = []): array
    {
        $batchId = Str::uuid()->toString();
        $jobs = [];
        $results = [];

        // 为无效表添加错误结果
        foreach ($invalidTables as $tableName) {
            $results[$tableName] = [
                'success' => false,
                'error' => "表 {$tableName} 的同步配置不存在"
            ];
        }

        // 为正在运行的表添加错误结果
        foreach ($runningTables as $tableName) {
            $results[$tableName] = [
                'success' => false,
                'error' => "表 {$tableName} 已有同步任务在运行中"
            ];
        }

        // 创建队列任务
        foreach ($tableNames as $tableName) {
            $jobs[] = new SyncTableJob($tableName, $syncType, $syncTables[$tableName], $batchId);
        }

        try {
            Log::info("开始并发同步批处理", [
                'batch_id' => $batchId,
                'tables' => $tableNames,
                'sync_type' => $syncType,
                'job_count' => count($jobs)
            ]);

            // 创建批处理任务
            $batch = Bus::batch($jobs)
                ->name("数据同步批处理 - {$syncType}")
                ->allowFailures() // 允许部分任务失败
                ->dispatch();

            // 异步模式：立即返回批处理信息，不等待完成
            foreach ($tableNames as $tableName) {
                $results[$tableName] = [
                    'success' => true,
                    'message' => '同步任务已启动',
                    'batch_id' => $batchId,
                    'laravel_batch_id' => $batch->id,
                    'status' => 'started',
                    'start_time' => now()->format('Y-m-d H:i:s')
                ];
            }

            Log::info("并发同步批处理已启动", [
                'batch_id' => $batchId,
                'laravel_batch_id' => $batch->id,
                'total_jobs' => $batch->totalJobs
            ]);

        } catch (Exception $e) {
            Log::error("并发同步批处理启动异常", [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 异常情况下的结果
            foreach ($tableNames as $tableName) {
                if (!isset($results[$tableName])) {
                    $results[$tableName] = [
                        'success' => false,
                        'error' => '批处理启动异常: ' . $e->getMessage()
                    ];
                }
            }
        }

        return $results;
    }

    /**
     * 获取批处理状态
     */
    public function getBatchStatus(string $batchId): array
    {
        try {
            // 查找Laravel批处理
            $batch = Bus::findBatch($batchId);
            
            if (!$batch) {
                return [
                    'success' => false,
                    'error' => '批处理不存在',
                    'status' => 'not_found'
                ];
            }

            $status = 'running';
            if ($batch->finished()) {
                $status = $batch->hasFailures() ? 'completed_with_errors' : 'completed';
            } elseif ($batch->cancelled()) {
                $status = 'cancelled';
            }

            // 获取每个表的详细状态
            $tableResults = [];
            $totalJobs = $batch->totalJobs;
            $processedJobs = $batch->processedJobs();
            $failedJobs = $batch->failedJobs;
            
            // 从同步日志中获取具体的表状态
            $recentLogs = SyncLog::where('start_time', '>=', $batch->createdAt)
                ->orderBy('start_time', 'desc')
                ->get()
                ->groupBy('table_name');

            foreach ($recentLogs as $tableName => $logs) {
                $latestLog = $logs->first();
                $tableResults[$tableName] = [
                    'table_name' => $tableName,
                    'status' => $latestLog->status,
                    'records_processed' => $latestLog->records_processed ?? 0,
                    'records_inserted' => $latestLog->records_inserted ?? 0,
                    'records_updated' => $latestLog->records_updated ?? 0,
                    'start_time' => $latestLog->start_time?->format('Y-m-d H:i:s'),
                    'end_time' => $latestLog->end_time?->format('Y-m-d H:i:s'),
                    'error_message' => $latestLog->error_message,
                ];
            }

            return [
                'success' => true,
                'batch_id' => $batchId,
                'status' => $status,
                'progress' => [
                    'total_jobs' => $totalJobs,
                    'processed_jobs' => $processedJobs,
                    'failed_jobs' => $failedJobs,
                    'pending_jobs' => $batch->pendingJobs,
                    'percentage' => $totalJobs > 0 ? round(($processedJobs / $totalJobs) * 100, 2) : 0
                ],
                'table_results' => $tableResults,
                'created_at' => $batch->createdAt->format('Y-m-d H:i:s'),
                'finished_at' => $batch->finishedAt?->format('Y-m-d H:i:s'),
            ];

        } catch (Exception $e) {
            Log::error("获取批处理状态失败", [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => '获取状态失败: ' . $e->getMessage(),
                'status' => 'error'
            ];
        }
    }

    /**
     * 简化的异步同步方法（保留向后兼容）
     * @deprecated 请使用 syncTablesParallel 方法
     */
    private function syncTableAsync(string $tableName, string $syncType, array $config): array
    {
        // 为保持向后兼容，保留此方法但标记为废弃
        return $this->syncTable($tableName, $syncType, $config);
    }

    /**
     * 使用已存在的同步日志进行同步
     */
    public function syncTableWithExistingLog(int $syncLogId, ?array $config = null): array
    {
        // 获取同步日志
        $syncLog = SyncLog::find($syncLogId);
        if (!$syncLog) {
            throw new Exception("同步日志记录不存在: {$syncLogId}");
        }

        $tableName = $syncLog->table_name;
        $syncType = $syncLog->sync_type;

        // 🔧 架构重构：复用核心同步逻辑，而不是重复代码
        return $this->performSyncWithLog($tableName, $syncType, $config, $syncLog);
    }

    /**
     * 同步单个表
     */
    public function syncTable(string $tableName, string $syncType = SyncLog::TYPE_INCREMENTAL, ?array $config = null): array
    {
        // 检查是否已有相同表的同步任务在运行
        $runningSync = SyncLog::where('table_name', $tableName)
            ->where('status', SyncLog::STATUS_RUNNING)
            ->where('created_at', '>', Carbon::now()->subHours(2)) // 只检查最近2小时的任务
            ->first();

        if ($runningSync) {
            throw new Exception("表 {$tableName} 已有同步任务在运行中，请稍后再试。任务开始时间: " . $runningSync->start_time);
        }

        $syncTables = $this->getSyncTables();
        $config = $config ?? $syncTables[$tableName] ?? [];
        
        if (empty($config)) {
            throw new Exception("表 {$tableName} 的同步配置不存在");
        }

        // 创建同步日志
        $syncLog = SyncLog::create([
            'table_name' => $tableName,
            'sync_type' => $syncType,
            'status' => SyncLog::STATUS_PENDING,
            'start_time' => Carbon::now()
        ]);

        // 🔧 架构重构：使用统一的核心同步方法
        return $this->performSyncWithLog($tableName, $syncType, $config, $syncLog);
    }

    /**
     * 核心同步方法 - 统一的业务逻辑入口
     */
    private function performSyncWithLog(string $tableName, string $syncType, ?array $config, SyncLog $syncLog): array
    {
        // 设置执行时间限制
        $maxExecutionTime = config('sync.execution.max_execution_time', 3600);
        set_time_limit($maxExecutionTime);
        
        // 设置内存限制
        $memoryLimit = config('sync.execution.memory_limit', '512M');
        ini_set('memory_limit', $memoryLimit);

        try {
            // 更新状态为运行中
            $syncLog->update(['status' => SyncLog::STATUS_RUNNING]);

            // 检查Oracle连接
            $oracleTest = $this->databaseService->testConnection('oracle');
            if (!$oracleTest['success']) {
                throw new Exception("Oracle数据库连接失败: " . $oracleTest['error']);
            }

            // 检查MySQL连接
            $mysqlTest = $this->databaseService->testConnection('mysql');
            if (!$mysqlTest['success']) {
                throw new Exception("MySQL数据库连接失败: " . $mysqlTest['error']);
            }

            // 执行同步
            $result = $this->performSync($tableName, $syncType, $config, $syncLog);

            // 更新同步日志
            $syncLog->update([
                'status' => $result['success'] ? SyncLog::STATUS_SUCCESS : SyncLog::STATUS_FAILED,
                'end_time' => Carbon::now(),
                'records_processed' => $result['records_processed'] ?? 0,
                'records_inserted' => $result['records_inserted'] ?? 0,
                'records_updated' => $result['records_updated'] ?? 0,
                'records_deleted' => $result['records_deleted'] ?? 0,
                'sync_details' => $result['details'] ?? null,
                'error_message' => $result['error'] ?? null
            ]);

            // 🚀 统一的自动转化逻辑 - 无论哪种调用方式都会执行
            Log::info("🔍 [performSyncWithLog] 检查自动转化", [
                'table' => $tableName,
                'success' => $result['success'],
                'shouldAutoTransform' => $this->shouldAutoTransform()
            ]);
            
            if ($result['success'] && $this->shouldAutoTransform()) {
                Log::info("✅ [performSyncWithLog] 开始自动转化", ['table' => $tableName]);
                $this->triggerAutoDataTransform($tableName, $syncType, $result);
            } else {
                Log::info("❌ [performSyncWithLog] 不满足自动转化条件", [
                    'table' => $tableName,
                    'success' => $result['success'],
                    'shouldAutoTransform' => $this->shouldAutoTransform()
                ]);
            }

            return $result;

        } catch (Exception $e) {
            // 更新同步日志为失败状态
            $syncLog->update([
                'status' => SyncLog::STATUS_FAILED,
                'end_time' => Carbon::now(),
                'error_message' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 执行实际的数据同步
     */
    private function performSync(string $tableName, string $syncType, array $config, SyncLog $syncLog): array
    {
        $primaryKey = $config['primary_key'] ?? 'ID';
        $timestampField = $config['timestamp_field'] ?? 'UPDATED_AT';
        $batchSize = $config['batch_size'] ?? 1000;

        $recordsProcessed = 0;
        $recordsInserted = 0;
        $recordsUpdated = 0;
        $recordsDeleted = 0;

        try {
            // 确保本地表存在
            $this->ensureLocalTableExists($tableName);

            // 根据同步类型选择不同的同步策略
            if ($syncType === SyncLog::TYPE_INCREMENTAL) {
                // 优先使用变更日志进行增量同步
                return $this->performIncrementalSyncWithChangeLog($tableName, $config, $syncLog);
            } else {
                // 全量同步逻辑保持不变
                return $this->performFullSync($tableName, $config, $syncLog);
            }

        } catch (Exception $e) {
            Log::error("同步表 {$tableName} 时发生错误", [
                'error' => $e->getMessage(),
                'processed' => $recordsProcessed
            ]);

            return [
                'success' => false,
                'records_processed' => $recordsProcessed,
                'records_inserted' => $recordsInserted,
                'records_updated' => $recordsUpdated,
                'records_deleted' => $recordsDeleted,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 基于变更日志的增量同步（新方法）
     */
    private function performIncrementalSyncWithChangeLog(string $tableName, array $config, SyncLog $syncLog): array
    {
        $primaryKey = $config['primary_key'] ?? 'ID';
        $batchSize = $config['batch_size'] ?? 1000;
        
        $recordsProcessed = 0;
        $recordsInserted = 0;
        $recordsUpdated = 0;
        $recordsDeleted = 0;

        try {
            // 构建变更日志查询条件 - 只基于sync_status=0，不考虑时间
            $changeLogQuery = "
                SELECT scl.operation, scl.pk_json, scl.pk_old_json, scl.id as change_id
                FROM sync_change_log scl 
                WHERE scl.table_name = ? 
                AND scl.sync_status = 0
                ORDER BY scl.change_time ASC
            ";
            
            $bindings = [$tableName];

            // 获取变更记录
            $changes = DB::connection('oracle')->select($changeLogQuery, $bindings);
            
            if (empty($changes)) {
                Log::info("表 {$tableName} 没有需要同步的变更记录");
                return [
                    'success' => true,
                    'records_processed' => 0,
                    'records_inserted' => 0,
                    'records_updated' => 0,
                    'records_deleted' => 0,
                    'message' => '没有需要同步的变更记录'
                ];
            }

            Log::info("表 {$tableName} 发现 " . count($changes) . " 条变更记录");

            // 分批处理变更记录
            $changeIds = [];
            $batchChanges = [];
            
            foreach ($changes as $index => $change) {
                $batchChanges[] = $change;
                $changeIds[] = $change->change_id;
                
                // 当达到批次大小或者是最后一条记录时，处理这批变更
                if (count($batchChanges) >= $batchSize || $index === count($changes) - 1) {
                    $batchResult = $this->processChangeLogBatch($tableName, $batchChanges, $primaryKey, $config);
                    
                    $recordsProcessed += $batchResult['processed'];
                    $recordsInserted += $batchResult['inserted'];
                    $recordsUpdated += $batchResult['updated'];
                    $recordsDeleted += $batchResult['deleted'];
                    
                    // 只标记成功处理的变更记录
                    $successfulChangeIds = $batchResult['successful_change_ids'] ?? [];
                    if (!empty($successfulChangeIds)) {
                        $this->markChangesAsSynced($successfulChangeIds);
                        
                        Log::info("标记变更记录状态", [
                            'table' => $tableName,
                            'batch_total' => count($changeIds),
                            'successful' => count($successfulChangeIds),
                            'failed' => count($changeIds) - count($successfulChangeIds)
                        ]);
                    } else {
                        Log::warning("批次中没有成功处理的变更记录", [
                            'table' => $tableName,
                            'batch_size' => count($changeIds)
                        ]);
                    }
                    
                    // 更新进度
                    $percentage = round((($index + 1) / count($changes)) * 100, 2);
                    Log::info("变更日志同步进度", [
                        'table' => $tableName,
                        'processed' => $index + 1,
                        'total' => count($changes),
                        'percentage' => $percentage,
                        'batch_successful' => count($successfulChangeIds),
                        'batch_total' => count($changeIds)
                    ]);
                    
                    // 实时更新数据库中的进度
                    $syncLog->update([
                        'records_processed' => $recordsProcessed,
                        'records_inserted' => $recordsInserted,
                        'records_updated' => $recordsUpdated,
                        'records_deleted' => $recordsDeleted,
                    ]);
                    
                    // 重置批次
                    $batchChanges = [];
                    $changeIds = [];
                }
            }

            return [
                'success' => true,
                'records_processed' => $recordsProcessed,
                'records_inserted' => $recordsInserted,
                'records_updated' => $recordsUpdated,
                'records_deleted' => $recordsDeleted,
                'message' => "基于变更日志成功同步 {$recordsProcessed} 条记录",
                'details' => [
                    'sync_type' => 'incremental_with_changelog',
                    'total_changes' => count($changes)
                ]
            ];

        } catch (Exception $e) {
            Log::error("基于变更日志的增量同步失败", [
                'table' => $tableName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new Exception("基于变更日志的增量同步失败: " . $e->getMessage());
        }
    }

    /**
     * 处理变更日志批次
     */
    private function processChangeLogBatch(string $tableName, array $changes, $primaryKey, array $config): array
    {
        $processed = 0;
        $inserted = 0;
        $updated = 0;
        $deleted = 0;
        $successfulChangeIds = []; // 记录成功处理的变更ID
        
        foreach ($changes as $change) {
            try {
                $operation = strtoupper($change->operation);
                $pkJson = json_decode($change->pk_json, true);
                
                if (!$pkJson) {
                    Log::warning("无效的主键JSON，跳过处理", [
                        'table' => $tableName,
                        'change_id' => $change->change_id,
                        'pk_json' => $change->pk_json
                    ]);
                    continue;
                }
                
                $operationSuccessful = false;
                
                switch ($operation) {
                    case 'INSERT':
                        $result = $this->syncRecordByPrimaryKey($tableName, $pkJson, $primaryKey, $config);
                        if ($result['action'] === 'inserted') {
                            $inserted++;
                            $operationSuccessful = true;
                        } elseif ($result['action'] === 'updated') {
                            $updated++;
                            $operationSuccessful = true;
                        } elseif ($result['action'] === 'not_found') {
                            // 记录不存在，也视为处理成功（可能已被删除）
                            $operationSuccessful = true;
                            Log::info("INSERT操作处理完成，但记录在Oracle中不存在", [
                                'table' => $tableName,
                                'change_id' => $change->change_id
                            ]);
                        }
                        break;
                        
                    case 'UPDATE':
                        // 处理UPDATE操作，可能涉及主键变更
                        $result = $this->handleUpdateOperation($tableName, $change, $primaryKey, $config);
                        $inserted += $result['inserted'];
                        $updated += $result['updated'];
                        $deleted += $result['deleted'];
                        
                        // 任何操作有结果都视为成功
                        if ($result['inserted'] > 0 || $result['updated'] > 0 || $result['deleted'] > 0) {
                            $operationSuccessful = true;
                        } else {
                            // 即使没有实际操作，也可能是合理的（如记录不存在）
                            $operationSuccessful = true;
                            Log::info("UPDATE操作处理完成，但没有实际变更", [
                                'table' => $tableName,
                                'change_id' => $change->change_id
                            ]);
                        }
                        break;
                        
                    case 'DELETE':
                        $result = $this->deleteRecordByPrimaryKey($tableName, $pkJson, $primaryKey);
                        if ($result['deleted']) {
                            $deleted++;
                            $operationSuccessful = true;
                        } else {
                            // 删除操作即使没有删除记录也视为成功（记录可能已不存在）
                            $operationSuccessful = true;
                            Log::info("DELETE操作处理完成，但记录不存在", [
                                'table' => $tableName,
                                'change_id' => $change->change_id
                            ]);
                        }
                        break;
                        
                    default:
                        Log::warning("未知的操作类型，跳过处理", [
                            'table' => $tableName,
                            'operation' => $operation,
                            'change_id' => $change->change_id
                        ]);
                        continue 2;
                }
                
                if ($operationSuccessful) {
                    $processed++;
                    $successfulChangeIds[] = $change->change_id;
                    
                    Log::debug("变更记录处理成功", [
                        'table' => $tableName,
                        'change_id' => $change->change_id,
                        'operation' => $operation
                    ]);
                }
                
            } catch (Exception $e) {
                Log::error("处理变更记录失败", [
                    'table' => $tableName,
                    'change_id' => $change->change_id,
                    'operation' => $change->operation ?? 'unknown',
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                // 处理失败的记录不加入成功列表，不会被标记为已同步
            }
        }
        
        return [
            'processed' => $processed,
            'inserted' => $inserted,
            'updated' => $updated,
            'deleted' => $deleted,
            'successful_change_ids' => $successfulChangeIds // 返回成功处理的变更ID
        ];
    }

    /**
     * 处理UPDATE操作，支持主键变更
     */
    private function handleUpdateOperation(string $tableName, object $change, $primaryKey, array $config): array
    {
        $inserted = 0;
        $updated = 0;
        $deleted = 0;
        
        $pkJson = json_decode($change->pk_json, true);
        $pkOldJson = isset($change->pk_old_json) ? json_decode($change->pk_old_json, true) : null;
        
        if (!$pkJson) {
            Log::warning("UPDATE操作的主键JSON解析失败", [
                'table' => $tableName,
                'change_id' => $change->change_id,
                'pk_json' => $change->pk_json
            ]);
            return ['inserted' => 0, 'updated' => 0, 'deleted' => 0];
        }
        
        // 检查是否有主键变更
        $primaryKeyChanged = false;
        if ($pkOldJson) {
            $primaryKeyChanged = $this->isPrimaryKeyChanged($pkJson, $pkOldJson, $primaryKey);
        }
        
        if ($primaryKeyChanged) {
            Log::info("检测到主键变更", [
                'table' => $tableName,
                'old_pk' => $pkOldJson,
                'new_pk' => $pkJson,
                'change_id' => $change->change_id
            ]);
            
            // 主键发生变更：先删除旧记录，再插入新记录
            // 1. 删除旧记录
            $deleteResult = $this->deleteRecordByPrimaryKey($tableName, $pkOldJson, $primaryKey);
            if ($deleteResult['deleted']) {
                $deleted++;
                Log::info("已删除旧主键记录", [
                    'table' => $tableName,
                    'old_pk' => $pkOldJson
                ]);
            }
            
            // 2. 插入新记录（按新主键）
            $insertResult = $this->syncRecordByPrimaryKey($tableName, $pkJson, $primaryKey, $config);
            if ($insertResult['action'] === 'inserted') {
                $inserted++;
                Log::info("已插入新主键记录", [
                    'table' => $tableName,
                    'new_pk' => $pkJson
                ]);
            }
        } else {
            // 主键未变更：普通更新操作
            $result = $this->syncRecordByPrimaryKey($tableName, $pkJson, $primaryKey, $config);
            if ($result['action'] === 'updated') {
                $updated++;
            } elseif ($result['action'] === 'inserted') {
                $inserted++;
            }
        }
        
        return [
            'inserted' => $inserted,
            'updated' => $updated,
            'deleted' => $deleted
        ];
    }
    
    /**
     * 检查主键是否发生变更
     */
    private function isPrimaryKeyChanged(array $newPk, array $oldPk, $primaryKey): bool
    {
        if (is_array($primaryKey)) {
            // 复合主键
            foreach ($primaryKey as $keyField) {
                $newValue = $newPk[$keyField] ?? null;
                $oldValue = $oldPk[$keyField] ?? null;
                
                if ($newValue !== $oldValue) {
                    return true;
                }
            }
        } else {
            // 单一主键
            $newValue = $newPk[$primaryKey] ?? null;
            $oldValue = $oldPk[$primaryKey] ?? null;
            
            return $newValue !== $oldValue;
        }
        
        return false;
    }

    /**
     * 根据主键同步单条记录
     */
    private function syncRecordByPrimaryKey(string $tableName, array $pkData, $primaryKey, array $config): array
    {
        // 构建查询条件
        $whereConditions = [];
        $bindings = [];
        
        // 添加主键条件
        if (is_array($primaryKey)) {
            foreach ($primaryKey as $keyField) {
                if (isset($pkData[$keyField])) {
                    $whereConditions[] = "{$keyField} = ?";
                    $bindings[] = $pkData[$keyField];
                }
            }
        } else {
            if (isset($pkData[$primaryKey])) {
                $whereConditions[] = "{$primaryKey} = ?";
                $bindings[] = $pkData[$primaryKey];
            }
        }
        
        // 添加过滤条件
        $filterConditions = $config['filter_conditions'] ?? [];
        foreach ($filterConditions as $field => $value) {
            $whereConditions[] = "{$field} = ?";
            $bindings[] = $value;
        }
        
        if (empty($whereConditions)) {
            throw new Exception("无法构建有效的查询条件");
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // 从Oracle获取最新数据
        $sql = "SELECT * FROM {$tableName} {$whereClause}";
        $oracleRecord = DB::connection('oracle')->select($sql, $bindings);
        
        if (empty($oracleRecord)) {
            // Oracle中没有找到记录，可能已被删除
            Log::warning("Oracle中未找到记录", [
                'table' => $tableName,
                'pk_data' => $pkData
            ]);
            return ['action' => 'not_found'];
        }
        
        $record = (array) $oracleRecord[0];
        
        // 转换字段名为大写（匹配MySQL表结构）
        $upperCaseRecord = [];
        foreach ($record as $key => $value) {
            $upperCaseRecord[strtoupper($key)] = $value;
        }
        
        // 检查MySQL中是否存在该记录
        $mysqlQuery = DB::connection('mysql')->table($tableName);
        
        if (is_array($primaryKey)) {
            foreach ($primaryKey as $keyField) {
                if (isset($upperCaseRecord[$keyField])) {
                    $mysqlQuery->where($keyField, $upperCaseRecord[$keyField]);
                }
            }
        } else {
            if (isset($upperCaseRecord[$primaryKey])) {
                $mysqlQuery->where($primaryKey, $upperCaseRecord[$primaryKey]);
            }
        }
        
        $exists = $mysqlQuery->exists();
        
        if ($exists) {
            // 更新记录
            $updateQuery = DB::connection('mysql')->table($tableName);
            
            if (is_array($primaryKey)) {
                foreach ($primaryKey as $keyField) {
                    if (isset($upperCaseRecord[$keyField])) {
                        $updateQuery->where($keyField, $upperCaseRecord[$keyField]);
                    }
                }
            } else {
                if (isset($upperCaseRecord[$primaryKey])) {
                    $updateQuery->where($primaryKey, $upperCaseRecord[$primaryKey]);
                }
            }
            
            $updateQuery->update($upperCaseRecord);
            return ['action' => 'updated'];
        } else {
            // 插入新记录
            DB::connection('mysql')->table($tableName)->insert($upperCaseRecord);
            return ['action' => 'inserted'];
        }
    }

    /**
     * 根据主键删除记录
     */
    private function deleteRecordByPrimaryKey(string $tableName, array $pkData, $primaryKey): array
    {
        $query = DB::connection('mysql')->table($tableName);
        
        if (is_array($primaryKey)) {
            foreach ($primaryKey as $keyField) {
                if (isset($pkData[$keyField])) {
                    $query->where($keyField, $pkData[$keyField]);
                }
            }
        } else {
            if (isset($pkData[$primaryKey])) {
                $query->where($primaryKey, $pkData[$primaryKey]);
            }
        }
        
        $deletedRows = $query->delete();
        
        return ['deleted' => $deletedRows > 0];
    }

    /**
     * 标记变更记录为已同步
     */
    private function markChangesAsSynced(array $changeIds): void
    {
        if (empty($changeIds)) {
            Log::debug("没有需要标记的变更记录");
            return;
        }
        
        try {
            // Oracle IN子句最多支持1000个表达式，需要分批处理
            $batchSize = 1000;
            $totalBatches = ceil(count($changeIds) / $batchSize);
            $totalAffectedRows = 0;
            
            Log::info("开始分批标记变更记录为已同步", [
                'total_change_ids' => count($changeIds),
                'batch_size' => $batchSize,
                'total_batches' => $totalBatches
            ]);
            
            for ($batch = 0; $batch < $totalBatches; $batch++) {
                $offset = $batch * $batchSize;
                $batchChangeIds = array_slice($changeIds, $offset, $batchSize);
                
                if (empty($batchChangeIds)) {
                    continue;
                }
                
                $placeholders = str_repeat('?,', count($batchChangeIds) - 1) . '?';
                $sql = "UPDATE sync_change_log SET sync_status = 1 WHERE id IN ({$placeholders})";
                
                $affectedRows = DB::connection('oracle')->update($sql, $batchChangeIds);
                $totalAffectedRows += $affectedRows;
                
                Log::debug("批次标记变更记录完成", [
                    'batch' => $batch + 1,
                    'batch_change_ids' => $batchChangeIds,
                    'batch_count' => count($batchChangeIds),
                    'affected_rows' => $affectedRows
                ]);
                
                // 验证批次更新是否成功
                if ($affectedRows !== count($batchChangeIds)) {
                    Log::warning("批次标记变更记录时受影响行数不匹配", [
                        'batch' => $batch + 1,
                        'expected' => count($batchChangeIds),
                        'actual' => $affectedRows,
                        'batch_change_ids' => $batchChangeIds
                    ]);
                    
                    // 检查这个批次中哪些记录没有被更新
                    $updatedIds = DB::connection('oracle')->select(
                        "SELECT id FROM sync_change_log WHERE id IN ({$placeholders}) AND sync_status = 1",
                        $batchChangeIds
                    );
                    
                    $updatedIdList = array_column($updatedIds, 'id');
                    $notUpdatedIds = array_diff($batchChangeIds, $updatedIdList);
                    
                    if (!empty($notUpdatedIds)) {
                        Log::error("批次中以下变更记录未能成功标记为已同步", [
                            'batch' => $batch + 1,
                            'not_updated_ids' => $notUpdatedIds
                        ]);
                    }
                }
            }
            
            Log::info("完成标记变更记录为已同步", [
                'total_change_ids' => count($changeIds),
                'total_batches' => $totalBatches,
                'total_affected_rows' => $totalAffectedRows
            ]);
            
            // 整体验证更新是否成功
            if ($totalAffectedRows !== count($changeIds)) {
                Log::warning("整体标记变更记录时受影响行数不匹配", [
                    'expected' => count($changeIds),
                    'actual' => $totalAffectedRows
                ]);
            }
            
        } catch (Exception $e) {
            Log::error("标记变更记录为已同步时发生错误", [
                'change_ids_count' => count($changeIds),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new Exception("标记变更记录为已同步失败: " . $e->getMessage());
        }
    }

    /**
     * 全量同步（保持原有逻辑）
     */
    private function performFullSync(string $tableName, array $config, SyncLog $syncLog): array
    {
        $primaryKey = $config['primary_key'] ?? 'ID';
        $timestampField = $config['timestamp_field'] ?? 'UPDATED_AT';
        $batchSize = $config['batch_size'] ?? 1000;

        $recordsProcessed = 0;
        $recordsInserted = 0;
        $recordsUpdated = 0;
        $recordsDeleted = 0;

        try {
            // 构建查询条件
            $whereConditions = [];
            $bindings = [];
            
            // 添加过滤条件
            $filterConditions = $config['filter_conditions'] ?? [];
            foreach ($filterConditions as $field => $value) {
                $whereConditions[] = "{$field} = ?";
                $bindings[] = $value;
            }
            
            $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

            // 获取Oracle数据总数
            $countSql = "SELECT COUNT(*) as total FROM {$tableName} {$whereClause}";
            $totalRecords = DB::connection('oracle')->select($countSql, $bindings)[0]->total ?? 0;

            if ($totalRecords == 0) {
                return [
                    'success' => true,
                    'records_processed' => 0,
                    'records_inserted' => 0,
                    'records_updated' => 0,
                    'records_deleted' => 0,
                    'message' => '没有需要同步的数据'
                ];
            }

            // 分批处理数据
            $offset = 0;
            while ($offset < $totalRecords) {
                // 处理复合主键的排序
                $orderByClause = is_array($primaryKey) ? implode(', ', $primaryKey) : $primaryKey;
                
                $sql = "SELECT * FROM (
                    SELECT a.*, ROWNUM rnum FROM (
                        SELECT * FROM {$tableName} {$whereClause} ORDER BY {$orderByClause}
                    ) a WHERE ROWNUM <= ?
                ) WHERE rnum > ?";
                
                $batchBindings = array_merge($bindings, [$offset + $batchSize, $offset]);
                $oracleData = DB::connection('oracle')->select($sql, $batchBindings);

                if (empty($oracleData)) {
                    break;
                }

                // 处理这批数据
                $batchResult = $this->processBatch($tableName, $oracleData, $primaryKey);
                
                $recordsProcessed += count($oracleData);
                $recordsInserted += $batchResult['inserted'];
                $recordsUpdated += $batchResult['updated'];

                $offset += $batchSize;

                // 更新进度，记录详细信息
                $percentage = round(($recordsProcessed / $totalRecords) * 100, 2);
                Log::info("同步表 {$tableName} 进度", [
                    'processed' => $recordsProcessed,
                    'total' => $totalRecords,
                    'percentage' => $percentage,
                    'batch' => $offset / $batchSize + 1,
                    'total_batches' => ceil($totalRecords / $batchSize)
                ]);
                
                // 实时更新数据库中的进度
                $syncLog->update([
                    'records_processed' => $recordsProcessed,
                    'records_inserted' => $recordsInserted,
                    'records_updated' => $recordsUpdated,
                ]);
                
                // 每处理5个批次或每10%进度强制刷新一次内存和连接
                if (($offset / $batchSize) % 5 == 0 || $percentage % 10 == 0) {
                    // 清理内存，防止大表同步内存溢出
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }
            }

            return [
                'success' => true,
                'records_processed' => $recordsProcessed,
                'records_inserted' => $recordsInserted,
                'records_updated' => $recordsUpdated,
                'records_deleted' => $recordsDeleted,
                'message' => "成功同步 {$recordsProcessed} 条记录",
                'details' => [
                    'sync_type' => 'full',
                    'total_records' => $totalRecords
                ]
            ];

        } catch (Exception $e) {
            Log::error("全量同步表 {$tableName} 时发生错误", [
                'error' => $e->getMessage(),
                'processed' => $recordsProcessed
            ]);

            return [
                'success' => false,
                'records_processed' => $recordsProcessed,
                'records_inserted' => $recordsInserted,
                'records_updated' => $recordsUpdated,
                'records_deleted' => $recordsDeleted,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理一批数据
     */
    private function processBatch(string $tableName, array $oracleData, $primaryKey): array
    {
        $inserted = 0;
        $updated = 0;
        $failed = 0;

        foreach ($oracleData as $record) {
            try {
                $recordArray = (array) $record;

                // 移除Oracle特有的字段（如ROWNUM）
                unset($recordArray['rnum']);

                // 转换字段名为大写（匹配MySQL表结构）
                $upperCaseRecord = [];
                foreach ($recordArray as $key => $value) {
                    $upperCaseRecord[strtoupper($key)] = $value;
                }

                // 验证主键字段是否存在
                $primaryKeyValues = [];
                if (is_array($primaryKey)) {
                    foreach ($primaryKey as $keyField) {
                        if (!isset($upperCaseRecord[$keyField])) {
                            throw new Exception("主键字段 {$keyField} 不存在或为空");
                        }
                        $primaryKeyValues[$keyField] = $upperCaseRecord[$keyField];
                    }
                } else {
                    if (!isset($upperCaseRecord[$primaryKey])) {
                        throw new Exception("主键字段 {$primaryKey} 不存在或为空");
                    }
                    $primaryKeyValues[$primaryKey] = $upperCaseRecord[$primaryKey];
                }

                // 使用单独的事务处理每条记录
                DB::connection('mysql')->transaction(function () use ($tableName, $upperCaseRecord, $primaryKeyValues, &$inserted, &$updated) {
                    // 构建主键查询条件
                    $query = DB::connection('mysql')->table($tableName);

                    foreach ($primaryKeyValues as $keyField => $keyValue) {
                        $query->where($keyField, $keyValue);
                    }

                    $exists = $query->exists();

                    if ($exists) {
                        // 更新记录
                        $updateQuery = DB::connection('mysql')->table($tableName);

                        foreach ($primaryKeyValues as $keyField => $keyValue) {
                            $updateQuery->where($keyField, $keyValue);
                        }

                        $updateQuery->update($upperCaseRecord);
                        $updated++;
                    } else {
                        // 插入新记录
                        DB::connection('mysql')
                            ->table($tableName)
                            ->insert($upperCaseRecord);
                        $inserted++;
                    }
                });

            } catch (Exception $e) {
                $failed++;

                // 记录失败的记录详情
                $primaryKeyStr = is_array($primaryKey) ? implode(',', $primaryKey) : $primaryKey;
                $primaryKeyValues = [];

                if (is_array($primaryKey)) {
                    foreach ($primaryKey as $keyField) {
                        $primaryKeyValues[] = $upperCaseRecord[$keyField] ?? 'NULL';
                    }
                } else {
                    $primaryKeyValues[] = $upperCaseRecord[$primaryKey] ?? 'NULL';
                }

                Log::warning("同步记录失败", [
                    'table' => $tableName,
                    'primary_key' => $primaryKeyStr,
                    'primary_key_values' => implode(',', $primaryKeyValues),
                    'error' => $e->getMessage(),
                    'record_data' => $upperCaseRecord
                ]);
            }
        }

        return [
            'inserted' => $inserted,
            'updated' => $updated,
            'failed' => $failed,
            'total_processed' => $inserted + $updated + $failed
        ];
    }

    /**
     * 确保本地表存在
     */
    private function ensureLocalTableExists(string $tableName): void
    {
        // 检查表是否存在
        $exists = DB::connection('mysql')
            ->select("SHOW TABLES LIKE '{$tableName}'");

        if (empty($exists)) {
            // 从Oracle获取表结构并创建本地表
            $this->createLocalTableFromOracle($tableName);
        }
    }

    /**
     * 从Oracle表结构创建本地表
     */
    private function createLocalTableFromOracle(string $tableName): void
    {
        // 获取Oracle表结构
        $columns = DB::connection('oracle')->select("
            SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE, NULLABLE
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = ? 
            ORDER BY COLUMN_ID
        ", [strtoupper($tableName)]);

        if (empty($columns)) {
            throw new Exception("无法获取Oracle表 {$tableName} 的结构信息");
        }

        // 构建CREATE TABLE语句
        $createSql = "CREATE TABLE `{$tableName}` (";
        $columnDefinitions = [];

        foreach ($columns as $column) {
            $columnDef = $this->convertOracleColumnToMySQL($column);
            $columnDefinitions[] = $columnDef;
        }

        $createSql .= implode(', ', $columnDefinitions) . ')';

        // 创建表
        DB::connection('mysql')->statement($createSql);
        
        Log::info("已创建本地表 {$tableName}");
    }

    /**
     * 转换Oracle列定义为MySQL列定义
     */
    private function convertOracleColumnToMySQL(object $column): string
    {
        $columnName = $column->column_name; // 保持原始大小写
        $dataType = $column->data_type;
        $length = $column->data_length;
        $precision = $column->data_precision;
        $scale = $column->data_scale;
        $nullable = $column->nullable === 'Y' ? '' : ' NOT NULL';

        $mysqlType = match($dataType) {
            'VARCHAR2' => "VARCHAR({$length})",
            'CHAR' => "CHAR({$length})",
            'NUMBER' => $precision && $scale ? "DECIMAL({$precision},{$scale})" : ($precision ? "DECIMAL({$precision})" : 'DECIMAL(10,0)'),
            'DATE' => 'DATETIME',
            'TIMESTAMP' => 'TIMESTAMP',
            'CLOB' => 'LONGTEXT',
            'BLOB' => 'LONGBLOB',
            default => 'TEXT'
        };

        return "`{$columnName}` {$mysqlType}{$nullable}";
    }

    /**
     * 获取同步统计信息
     */
    public function getSyncStats(): array
    {
        $stats = [];
        
        $syncTables = $this->getSyncTables();
        $tableNames = array_keys($syncTables);
        
        // 🚀 优化：使用单个查询获取所有表的统计信息
        $allStats = SyncLog::whereIn('table_name', $tableNames)
            ->selectRaw('
                table_name,
                COUNT(*) as total_syncs,
                MAX(start_time) as latest_sync_time,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as success_count
            ', [SyncLog::STATUS_SUCCESS])
            ->groupBy('table_name')
            ->get()
            ->keyBy('table_name');
        
        // 🚀 优化：使用窗口函数一次性获取各表最新的同步记录
        $latestSyncsRaw = DB::select("
            SELECT * FROM (
                SELECT *,
                       ROW_NUMBER() OVER (PARTITION BY table_name ORDER BY created_at DESC) as row_num
                FROM sync_logs 
                WHERE table_name IN ('" . implode("', '", $tableNames) . "')
            ) ranked
            WHERE row_num = 1
        ");
        
        $latestSyncs = [];
        foreach ($latestSyncsRaw as $syncData) {
            // 转换为SyncLog模型实例
            $latestSyncs[$syncData->table_name] = SyncLog::hydrate([(array)$syncData])->first();
        }
        
        foreach ($tableNames as $tableName) {
            $stat = $allStats->get($tableName);
            
            $successRate = 0.0;
            if ($stat && $stat->total_syncs > 0) {
                $successRate = round(($stat->success_count / $stat->total_syncs) * 100, 2);
            }
            
            // 兼容SyncStatus命令的数据结构
            $stats[$tableName] = [
                'total_syncs' => $stat ? $stat->total_syncs : 0,
                'success_rate' => $successRate,
                'latest_sync' => $latestSyncs[$tableName] // 添加latest_sync字段
            ];
        }
        
        return $stats;
    }

    /**
     * 计算成功率
     */
    private function calculateSuccessRate(string $tableName): float
    {
        $recentLogs = SyncLog::where('table_name', $tableName)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->get();

        if ($recentLogs->isEmpty()) {
            return 0.0;
        }

        $successCount = $recentLogs->where('status', SyncLog::STATUS_SUCCESS)->count();
        return round(($successCount / $recentLogs->count()) * 100, 2);
    }

    /**
     * 检查队列系统状态
     */
    private function checkQueueStatus(): array
    {
        try {
            // 检查是否有队列工作进程在运行
            $queueWorkers = [];
            $command = "ps aux | grep 'queue:work' | grep -v grep";
            $output = [];
            $returnCode = 0;
            
            exec($command, $output, $returnCode);
            
            $isRunning = !empty($output);
            $workerCount = count($output);
            
            return [
                'running' => $isRunning,
                'worker_count' => $workerCount,
                'workers' => $output
            ];
            
        } catch (Exception $e) {
            Log::error("检查队列状态失败", [
                'error' => $e->getMessage()
            ]);
            
            return [
                'running' => false,
                'worker_count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 顺序同步多个表（当队列系统不可用时的后备方案）
     */
    private function syncTablesSequentially(array $tableNames, string $syncType, array $syncTables, array $invalidTables = [], array $runningTables = []): array
    {
        $results = [];

        // 为无效表添加错误结果
        foreach ($invalidTables as $tableName) {
            $results[$tableName] = [
                'success' => false,
                'error' => "表 {$tableName} 没有有效的同步配置"
            ];
        }

        // 为正在运行的表添加错误结果
        foreach ($runningTables as $tableName) {
            $results[$tableName] = [
                'success' => false,
                'error' => "表 {$tableName} 已有同步任务在运行中"
            ];
        }

        // 顺序执行有效的表
        foreach ($tableNames as $tableName) {
            try {
                Log::info("顺序同步表: {$tableName}", [
                    'sync_type' => $syncType,
                    'mode' => 'sequential'
                ]);
                
                $result = $this->syncTable($tableName, $syncType, $syncTables[$tableName]);
                $results[$tableName] = $result;
                
                Log::info("顺序同步表完成: {$tableName}", [
                    'success' => $result['success'],
                    'records_processed' => $result['records_processed'] ?? 0
                ]);
                
            } catch (Exception $e) {
                Log::error("顺序同步表失败: {$tableName}", [
                    'error' => $e->getMessage()
                ]);
                
                $results[$tableName] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 检查是否存在重复的转化任务
     * 
     * @param string $transformType 转化类型
     * @param string $companyCode 公司代码
     * @param string $triggerTable 触发表名
     * @return bool 是否存在重复任务
     */
    private function isDuplicateTransformTask(string $transformType, string $companyCode, string $triggerTable = null): bool
    {
        try {
            // 🔧 优化：更短的去重窗口，避免过度阻塞
            $dedupWindow = config('sync.auto_transform.conditions.dedup_window', 120); // 从5分钟改为2分钟
            $cutoffTime = now()->subSeconds($dedupWindow);
            
            // 🔧 优化：更精确的任务检查，包含触发表信息
            $cacheKey = "transform_dedup_{$transformType}_{$companyCode}";
            if ($triggerTable) {
                $cacheKey .= "_{$triggerTable}";
            }
            
            // 检查缓存中的去重标记
            if (Cache::has($cacheKey)) {
                Log::info("转化任务去重：缓存命中", [
                    'transform_type' => $transformType,
                    'company_code' => $companyCode,
                    'trigger_table' => $triggerTable,
                    'cache_key' => $cacheKey
                ]);
                return true;
            }
            
            // 🔧 优化：检查队列中是否有相同的转化任务（更精确的匹配）
            $existingJobs = DB::table('jobs')
                ->where('queue', 'transform')
                ->where('created_at', '>=', $cutoffTime)
                ->where('payload', 'LIKE', '%TransformDataJob%')
                ->where('payload', 'LIKE', '%"' . $transformType . '"%')
                ->where('payload', 'LIKE', '%"' . $companyCode . '"%');
                
            // 如果有触发表，进一步过滤
            if ($triggerTable) {
                $existingJobs = $existingJobs->where('payload', 'LIKE', '%"trigger_table":"' . $triggerTable . '"%');
            }
                
            $jobCount = $existingJobs->count();
            
            if ($jobCount > 0) {
                Log::info("转化任务去重：队列中存在相同任务", [
                    'transform_type' => $transformType,
                    'company_code' => $companyCode,
                    'trigger_table' => $triggerTable,
                    'existing_jobs' => $jobCount
                ]);
                
                // 🔧 设置去重缓存，避免频繁查询数据库
                Cache::put($cacheKey, true, $dedupWindow);
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            Log::warning("检查重复转化任务失败，允许继续", [
                'transform_type' => $transformType,
                'company_code' => $companyCode,
                'trigger_table' => $triggerTable,
                'error' => $e->getMessage()
            ]);
            return false; // 出错时不阻止转化任务
        }
    }

    /**
     * 🔧 新增：记录转化任务执行状态
     * 
     * @param string $transformType 转化类型
     * @param string $companyCode 公司代码
     * @param string $triggerTable 触发表名
     * @param string $status 状态 (started|completed|failed)
     */
    private function recordTransformTaskStatus(string $transformType, string $companyCode, string $triggerTable = null, string $status = 'started'): void
    {
        try {
            $cacheKey = "transform_status_{$transformType}_{$companyCode}";
            if ($triggerTable) {
                $cacheKey .= "_{$triggerTable}";
            }
            
            $statusData = [
                'transform_type' => $transformType,
                'company_code' => $companyCode,
                'trigger_table' => $triggerTable,
                'status' => $status,
                'timestamp' => now()->timestamp
            ];
            
            // 缓存状态信息5分钟
            Cache::put($cacheKey, $statusData, 300);
            
            Log::debug("记录转化任务状态", $statusData);
            
        } catch (Exception $e) {
            Log::warning("记录转化任务状态失败", [
                'transform_type' => $transformType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 计算转化任务的智能延迟时间
     * 
     * @param string $transformType 转化类型
     * @param string $tableName 触发的表名
     * @return int 延迟秒数
     */
    private function calculateTransformDelay(string $transformType, string $tableName): int
    {
        // 获取配置的延迟时间
        $typeDelays = config('sync.auto_transform.type_delays', []);
        $baseDelay = $typeDelays[$transformType] ?? config('sync.auto_transform.delay', 10);
        
        // 获取相关表配置
        $relatedTables = config('sync.auto_transform.related_tables', []);
        
        if (!isset($relatedTables[$transformType])) {
            // 单表转化，使用基础延迟
            return $baseDelay;
        }
        
        $allRelatedTables = $relatedTables[$transformType];
        
        // 检查相关表的最近同步时间
        try {
            $recentSyncThreshold = now()->subMinutes(10); // 10分钟内的同步被认为是"最近"
            
            $recentSyncCount = 0;
            foreach ($allRelatedTables as $relatedTable) {
                $latestSync = SyncLog::where('table_name', $relatedTable)
                    ->where('status', SyncLog::STATUS_SUCCESS)
                    ->where('end_time', '>=', $recentSyncThreshold)
                    ->exists();
                    
                if ($latestSync) {
                    $recentSyncCount++;
                }
            }
            
            // 如果大部分相关表都在近期同步过，使用较短延迟
            $syncRatio = $recentSyncCount / count($allRelatedTables);
            if ($syncRatio >= 0.8) {
                $adjustedDelay = intval($baseDelay * 0.5); // 减少50%延迟
                Log::info("相关表大部分已同步，减少转化延迟", [
                    'transform_type' => $transformType,
                    'trigger_table' => $tableName,
                    'sync_ratio' => $syncRatio,
                    'base_delay' => $baseDelay,
                    'adjusted_delay' => $adjustedDelay
                ]);
                return max($adjustedDelay, 10); // 最少10秒
            }
            
        } catch (Exception $e) {
            Log::warning("计算智能延迟时间失败，使用基础延迟", [
                'transform_type' => $transformType,
                'base_delay' => $baseDelay,
                'error' => $e->getMessage()
            ]);
        }
        
        return $baseDelay;
    }

    /**
     * 自动触发数据转化任务
     * 
     * @param string $tableName 同步完成的表名
     * @param string $syncType 同步类型
     * @param array $syncResult 同步结果
     */
    private function triggerAutoDataTransform(string $tableName, string $syncType, array $syncResult): void
    {
        try {
            $transformMapping = $this->getTableTransformMapping();
            
            // 检查表是否需要转化
            if (!isset($transformMapping[$tableName])) {
                Log::info("表 {$tableName} 不需要数据转化，跳过自动转化");
                return;
            }

            $transformTypes = $transformMapping[$tableName];
            $companyCode = config('sync.auto_transform.company_code', 'TB');
            $locale = config('sync.auto_transform.locale', 'zh_CN');
            
            // 为每个转化类型单独处理
            foreach ($transformTypes as $transformType) {
                // 检查是否启用去重
                if (config('sync.auto_transform.conditions.deduplication', true)) {
                    if ($this->isDuplicateTransformTask($transformType, $companyCode, $tableName)) {
                        Log::info("转化任务去重：跳过重复的 {$transformType} 转化任务", [
                            'table' => $tableName,
                            'transform_type' => $transformType
                        ]);
                        continue;
                    }
                }
                
                // 🔧 优化：获取该转化类型的智能延迟时间
                $delay = $this->calculateTransformDelay($transformType, $tableName);
                
                // 🔧 优化：准备转化选项，包含增量模式和批量处理参数
                $options = [
                    'locale' => $locale,
                    'triggered_by' => 'auto_sync',
                    'trigger_table' => $tableName,
                    'sync_type' => $syncType,
                    'incremental' => true, // 🔧 默认使用增量转化
                    'batch_size' => $this->getBatchSizeForTransformType($transformType), // 🔧 动态批量大小
                    'sync_result' => [
                        'records_processed' => $syncResult['records_processed'] ?? 0,
                        'records_inserted' => $syncResult['records_inserted'] ?? 0,
                        'records_updated' => $syncResult['records_updated'] ?? 0,
                        'records_deleted' => $syncResult['records_deleted'] ?? 0
                    ]
                ];

                // 🔧 记录任务启动状态
                $this->recordTransformTaskStatus($transformType, $companyCode, $tableName, 'started');

                // 派发数据转化任务到队列
                TransformDataJob::dispatch([$transformType], $companyCode, $options)
                    ->delay(now()->addSeconds($delay))
                    ->onQueue('transform'); // 使用专门的转化队列

                Log::info("已自动触发数据转化任务", [
                    'table' => $tableName,
                    'transform_type' => $transformType,
                    'company_code' => $companyCode,
                    'delay_seconds' => $delay,
                    'options' => $options,
                    'sync_result' => $syncResult
                ]);
            }

        } catch (Exception $e) {
            Log::error("自动触发数据转化失败", [
                'table' => $tableName,
                'sync_type' => $syncType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 不抛出异常，避免影响同步流程
        }
    }

    /**
     * 🔧 新增：根据转化类型获取合适的批量大小
     * 
     * @param string $transformType 转化类型
     * @return int 批量大小
     */
    private function getBatchSizeForTransformType(string $transformType): int
    {
        // 根据不同转化类型的数据量特点设置不同的批量大小
        $batchSizes = [
            'material' => 500,  // 物料数据量大，使用较小批量
            'customer' => 1000, // 客户数据适中
            'category' => 2000, // 分类数据较少，可以大批量
            'bom' => 800       // BOM数据复杂，使用中等批量
        ];
        
        return $batchSizes[$transformType] ?? 1000; // 默认1000
    }
} 