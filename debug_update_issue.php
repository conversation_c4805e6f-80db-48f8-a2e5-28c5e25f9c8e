<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 调试UPDATE问题 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 随机选择一些Oracle记录进行测试
    echo "🔍 获取Oracle测试数据..." . PHP_EOL;
    $oracleRecords = DB::connection('oracle')
        ->table('BMBA_T')
        ->where('BMBAENT', 40)
        ->limit(10)
        ->get();
    
    echo "获取到 " . $oracleRecords->count() . " 条Oracle记录" . PHP_EOL . PHP_EOL;
    
    $updateSuccessCount = 0;
    $updateFailCount = 0;
    $notFoundCount = 0;
    $affectedRowsZero = 0;
    
    foreach ($oracleRecords as $index => $record) {
        echo "测试记录 " . ($index + 1) . ":" . PHP_EOL;
        
        $recordArray = (array) $record;

        // 移除Oracle特有的字段（如ROWNUM）
        unset($recordArray['rnum']);
        unset($recordArray['RNUM']);
        unset($recordArray['rn']);
        unset($recordArray['RN']);

        // 转换字段名为大写
        $upperCaseRecord = [];
        foreach ($recordArray as $key => $value) {
            $upperCaseRecord[strtoupper($key)] = $value;
        }
        
        // 构建主键条件
        $primaryKey = ['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004'];
        $primaryKeyValues = [];
        foreach ($primaryKey as $keyField) {
            $primaryKeyValues[$keyField] = $upperCaseRecord[$keyField];
        }
        
        echo "  主键: " . json_encode($primaryKeyValues) . PHP_EOL;
        
        // 检查记录是否存在
        $query = DB::table('BMBA_T');
        foreach ($primaryKeyValues as $keyField => $keyValue) {
            $query->where($keyField, $keyValue);
        }
        
        $exists = $query->exists();
        echo "  MySQL中存在: " . ($exists ? '是' : '否') . PHP_EOL;
        
        if (!$exists) {
            $notFoundCount++;
            echo "  ❌ 记录在MySQL中不存在！" . PHP_EOL;
            continue;
        }
        
        // 获取MySQL中的当前记录
        $mysqlRecord = $query->first();
        $mysqlArray = (array) $mysqlRecord;
        
        // 比较数据是否相同
        $differences = [];
        foreach ($upperCaseRecord as $field => $oracleValue) {
            if (isset($mysqlArray[$field])) {
                $mysqlValue = $mysqlArray[$field];
                if ($oracleValue != $mysqlValue) {
                    $differences[$field] = [
                        'oracle' => $oracleValue,
                        'mysql' => $mysqlValue
                    ];
                }
            }
        }
        
        echo "  数据差异: " . count($differences) . " 个字段" . PHP_EOL;
        if (count($differences) > 0) {
            echo "  差异字段: " . implode(', ', array_keys($differences)) . PHP_EOL;
        }
        
        // 尝试执行UPDATE
        try {
            $updateQuery = DB::table('BMBA_T');
            foreach ($primaryKeyValues as $keyField => $keyValue) {
                $updateQuery->where($keyField, $keyValue);
            }
            
            $affectedRows = $updateQuery->update($upperCaseRecord);
            echo "  UPDATE影响行数: " . $affectedRows . PHP_EOL;
            
            if ($affectedRows > 0) {
                $updateSuccessCount++;
                echo "  ✅ UPDATE成功" . PHP_EOL;
            } else {
                $affectedRowsZero++;
                echo "  ⚠️  UPDATE返回0行（可能数据相同或其他问题）" . PHP_EOL;
            }
            
        } catch (Exception $e) {
            $updateFailCount++;
            echo "  ❌ UPDATE失败: " . $e->getMessage() . PHP_EOL;
        }
        
        echo PHP_EOL;
    }
    
    // 统计结果
    echo "📊 测试结果统计:" . PHP_EOL;
    echo "  总测试记录: " . $oracleRecords->count() . PHP_EOL;
    echo "  MySQL中不存在: " . $notFoundCount . PHP_EOL;
    echo "  UPDATE成功(影响>0行): " . $updateSuccessCount . PHP_EOL;
    echo "  UPDATE返回0行: " . $affectedRowsZero . PHP_EOL;
    echo "  UPDATE失败: " . $updateFailCount . PHP_EOL . PHP_EOL;
    
    // 分析问题
    echo "🔧 问题分析:" . PHP_EOL;
    if ($notFoundCount > 0) {
        echo "  ⚠️  发现 {$notFoundCount} 条记录在MySQL中不存在，但同步日志显示已更新" . PHP_EOL;
    }
    if ($affectedRowsZero > 0) {
        echo "  ⚠️  发现 {$affectedRowsZero} 条记录UPDATE返回0行，可能是数据相同或WHERE条件问题" . PHP_EOL;
    }
    if ($updateFailCount > 0) {
        echo "  ❌ 发现 {$updateFailCount} 条记录UPDATE失败" . PHP_EOL;
    }
    
    // 进一步检查：随机验证一些"应该存在"的记录
    echo PHP_EOL . "🔍 进一步验证：检查同步日志中的记录是否真的存在..." . PHP_EOL;
    
    // 获取最新同步的一些记录ID范围
    $recentOracleRecords = DB::connection('oracle')
        ->table('BMBA_T')
        ->where('BMBAENT', 40)
        ->orderBy('BMBA001')
        ->limit(5)
        ->get(['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004']);
    
    $missingInMysql = 0;
    foreach ($recentOracleRecords as $record) {
        $recordArray = (array) $record;
        $upperCaseRecord = [];
        foreach ($recordArray as $key => $value) {
            $upperCaseRecord[strtoupper($key)] = $value;
        }
        
        $query = DB::table('BMBA_T');
        foreach ($upperCaseRecord as $keyField => $keyValue) {
            $query->where($keyField, $keyValue);
        }
        
        if (!$query->exists()) {
            $missingInMysql++;
            echo "  ❌ 缺失记录: " . json_encode($upperCaseRecord) . PHP_EOL;
        }
    }
    
    if ($missingInMysql > 0) {
        echo "  发现 {$missingInMysql}/5 条Oracle记录在MySQL中缺失！" . PHP_EOL;
    } else {
        echo "  ✅ 抽查的5条记录都在MySQL中存在" . PHP_EOL;
    }

} catch (Exception $e) {
    echo "❌ 调试过程中发生错误: " . $e->getMessage() . PHP_EOL;
    echo "错误详情: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== 调试完成 ===" . PHP_EOL;
