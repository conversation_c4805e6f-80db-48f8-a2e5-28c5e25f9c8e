<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\SyncLog;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== BMBA_T 同步数据差异诊断 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 基础数据统计
    echo "1. 基础数据统计:" . PHP_EOL;
    $oracleTotal = DB::connection('oracle')->table('BMBA_T')->count();
    $oracleFiltered = DB::connection('oracle')->table('BMBA_T')->where('BMBAENT', 40)->count();
    $mysqlTotal = DB::table('BMBA_T')->count();
    $mysqlFiltered = DB::table('BMBA_T')->where('BMBAENT', 40)->count();
    
    echo "   Oracle BMBA_T 总记录数: " . number_format($oracleTotal) . PHP_EOL;
    echo "   Oracle BMBA_T (BMBAENT=40): " . number_format($oracleFiltered) . PHP_EOL;
    echo "   MySQL BMBA_T 总记录数: " . number_format($mysqlTotal) . PHP_EOL;
    echo "   MySQL BMBA_T (BMBAENT=40): " . number_format($mysqlFiltered) . PHP_EOL;
    echo "   差异: " . number_format($oracleFiltered - $mysqlTotal) . " 条记录" . PHP_EOL . PHP_EOL;

    // 2. 最新同步日志
    echo "2. 最新同步日志:" . PHP_EOL;
    $latestSync = SyncLog::where('table_name', 'BMBA_T')->orderBy('id', 'desc')->first();
    if ($latestSync) {
        echo "   同步ID: " . $latestSync->id . PHP_EOL;
        echo "   状态: " . $latestSync->status . PHP_EOL;
        echo "   处理记录数: " . number_format($latestSync->records_processed) . PHP_EOL;
        echo "   插入记录数: " . number_format($latestSync->records_inserted) . PHP_EOL;
        echo "   更新记录数: " . number_format($latestSync->records_updated) . PHP_EOL;
        echo "   删除记录数: " . number_format($latestSync->records_deleted) . PHP_EOL;
        $totalProcessed = $latestSync->records_inserted + $latestSync->records_updated;
        echo "   实际处理总数: " . number_format($totalProcessed) . PHP_EOL;
        echo "   差异分析: 处理了 " . number_format($latestSync->records_processed) . " 条，但只有 " . number_format($totalProcessed) . " 条成功" . PHP_EOL;
    }
    echo PHP_EOL;

    // 3. 检查主键范围
    echo "3. 主键范围分析:" . PHP_EOL;
    
    // Oracle主键范围
    $oracleMinMax = DB::connection('oracle')
        ->table('BMBA_T')
        ->where('BMBAENT', 40)
        ->selectRaw('MIN(BMBA001) as min_bmba001, MAX(BMBA001) as max_bmba001, COUNT(DISTINCT BMBA001) as distinct_bmba001')
        ->first();
    
    // MySQL主键范围
    $mysqlMinMax = DB::table('BMBA_T')
        ->selectRaw('MIN(BMBA001) as min_bmba001, MAX(BMBA001) as max_bmba001, COUNT(DISTINCT BMBA001) as distinct_bmba001')
        ->first();
    
    echo "   Oracle BMBA001 范围: " . $oracleMinMax->min_bmba001 . " ~ " . $oracleMinMax->max_bmba001 . " (去重: " . $oracleMinMax->distinct_bmba001 . ")" . PHP_EOL;
    echo "   MySQL BMBA001 范围: " . $mysqlMinMax->min_bmba001 . " ~ " . $mysqlMinMax->max_bmba001 . " (去重: " . $mysqlMinMax->distinct_bmba001 . ")" . PHP_EOL;
    echo PHP_EOL;

    // 4. 检查是否有重复主键
    echo "4. 重复主键检查:" . PHP_EOL;
    $duplicates = DB::table('BMBA_T')
        ->select('BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004')
        ->selectRaw('COUNT(*) as count')
        ->groupBy('BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004')
        ->having('count', '>', 1)
        ->limit(10)
        ->get();
    
    if ($duplicates->count() > 0) {
        echo "   发现重复主键记录:" . PHP_EOL;
        foreach ($duplicates as $dup) {
            echo "   - BMBAENT={$dup->BMBAENT}, BMBASITE={$dup->BMBASITE}, BMBA001={$dup->BMBA001}, BMBA003={$dup->BMBA003}, BMBA004={$dup->BMBA004} (重复{$dup->count}次)" . PHP_EOL;
        }
    } else {
        echo "   ✅ 未发现重复主键记录" . PHP_EOL;
    }
    echo PHP_EOL;

    // 5. 检查数据分布
    echo "5. 数据分布检查:" . PHP_EOL;

    // 检查Oracle和MySQL中的BMBA001分布
    $oracleDistribution = DB::connection('oracle')
        ->table('BMBA_T')
        ->where('BMBAENT', 40)
        ->selectRaw('SUBSTR(BMBA001, 1, 4) as prefix, COUNT(*) as count')
        ->groupBy(DB::raw('SUBSTR(BMBA001, 1, 4)'))
        ->orderBy('prefix')
        ->limit(10)
        ->get();

    $mysqlDistribution = DB::table('BMBA_T')
        ->selectRaw('SUBSTR(BMBA001, 1, 4) as prefix, COUNT(*) as count')
        ->groupBy(DB::raw('SUBSTR(BMBA001, 1, 4)'))
        ->orderBy('prefix')
        ->limit(10)
        ->get();

    echo "   Oracle前缀分布 vs MySQL前缀分布:" . PHP_EOL;
    $oracleMap = $oracleDistribution->keyBy('prefix');
    $mysqlMap = $mysqlDistribution->keyBy('prefix');

    foreach ($oracleDistribution as $item) {
        $prefix = $item->prefix;
        $oracleCount = $item->count;
        $mysqlCount = isset($mysqlMap[$prefix]) ? $mysqlMap[$prefix]->count : 0;
        $diff = $oracleCount - $mysqlCount;
        $status = $diff == 0 ? '✅' : '❌';
        echo "   {$status} {$prefix}*: Oracle={$oracleCount}, MySQL={$mysqlCount}, 差异={$diff}" . PHP_EOL;
    }
    echo PHP_EOL;

    // 6. 建议
    echo "6. 诊断结论和建议:" . PHP_EOL;
    $difference = $oracleFiltered - $mysqlTotal;
    if ($difference > 0) {
        echo "   ⚠️  发现数据不一致: MySQL中缺少 " . number_format($difference) . " 条记录" . PHP_EOL;
        echo "   📋 可能原因:" . PHP_EOL;
        echo "      - 同步过程中某些记录插入失败但未记录错误" . PHP_EOL;
        echo "      - 数据类型转换问题导致插入失败" . PHP_EOL;
        echo "      - 字符集或编码问题" . PHP_EOL;
        echo "      - MySQL表约束导致插入失败" . PHP_EOL;
        echo "   🔧 建议操作:" . PHP_EOL;
        echo "      1. 重新执行全量同步并监控错误日志" . PHP_EOL;
        echo "      2. 检查MySQL表结构和约束" . PHP_EOL;
        echo "      3. 启用详细日志记录失败的记录" . PHP_EOL;
    } else {
        echo "   ✅ 数据一致性正常" . PHP_EOL;
    }

} catch (Exception $e) {
    echo "❌ 诊断过程中发生错误: " . $e->getMessage() . PHP_EOL;
    echo "错误详情: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== 诊断完成 ===" . PHP_EOL;
