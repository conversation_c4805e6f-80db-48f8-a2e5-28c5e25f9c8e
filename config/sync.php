<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 数据同步配置
    |--------------------------------------------------------------------------
    |
    | 这里配置Oracle数据库同步相关的设置
    |
    */

    // 源数据库连接名称
    'source_connection' => env('SYNC_SOURCE_CONNECTION', 'oracle'),

    // 目标数据库连接名称
    'target_connection' => env('SYNC_TARGET_CONNECTION', 'mysql'),

    // 需要同步的表配置 - 基于实际Oracle表结构
    'tables' => [
        'BMAA_T' => [
            'primary_key' => ['BMAAENT', 'BMAASITE', 'BMAA001'], // 复合主键
            'timestamp_field' => 'BMAAMODDT', // 修改时间戳
            'filter_conditions' => ['BMAAENT' => 40], // 过滤条件：只同步BMAAENT=40的数据
            'batch_size' => env('SYNC_BMAA_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_BMAA_T_ENABLED', true),
        ],
        'BMBA_T' => [
            'primary_key' => ['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004'], // 复合主键
            'timestamp_field' => null, // BMBA_T表没有修改时间戳字段，使用全量同步
            'filter_conditions' => ['BMBAENT' => 40], // 过滤条件：只同步BMBAENT=40的数据
            'batch_size' => env('SYNC_BMBA_T_BATCH_SIZE', 1000), // 减小批次大小以提高稳定性
            'enabled' => env('SYNC_BMBA_T_ENABLED', true),
        ],
        'IMAA_T' => [
            'primary_key' => ['IMAAENT', 'IMAA001'], // 复合主键
            'timestamp_field' => 'IMAAMODDT', // 修改时间戳
            'filter_conditions' => ['IMAAENT' => 40], // 过滤条件：只同步IMAAENT=40的数据
            'batch_size' => env('SYNC_IMAA_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_IMAA_T_ENABLED', true),
        ],
        'IMAAL_T' => [
            'primary_key' => ['IMAALENT', 'IMAAL001', 'IMAAL002'], // 复合主键
            'timestamp_field' => null, // 无时间戳字段，使用全量同步
            'filter_conditions' => ['IMAALENT' => 40], // 过滤条件：只同步IMAALENT=40的数据
            'batch_size' => env('SYNC_IMAAL_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_IMAAL_T_ENABLED', true),
        ],
        'IMAF_T' => [
            'primary_key' => ['IMAFENT', 'IMAFSITE', 'IMAF001'], // 复合主键
            'timestamp_field' => 'IMAFMODDT', // 修改时间戳
            'filter_conditions' => ['IMAFENT' => 40], // 过滤条件：只同步IMAFENT=40的数据
            'batch_size' => env('SYNC_IMAF_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_IMAF_T_ENABLED', true),
        ],
        'RTAXL_T' => [
            'primary_key' => ['RTAXLENT', 'RTAXL001', 'RTAXL002'], // 复合主键
            'timestamp_field' => null, // 无时间戳字段，使用全量同步
            'filter_conditions' => ['RTAXLENT' => 40], // 过滤条件：只同步RTAXLENT=40的数据
            'batch_size' => env('SYNC_RTAXL_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_RTAXL_T_ENABLED', true),
        ],
        'PMAB_T' => [
            'primary_key' => ['PMABENT', 'PMABSITE', 'PMAB001'], // 复合主键
            'timestamp_field' => 'PMABMODDT', // 修改时间戳
            'filter_conditions' => ['PMABENT' => 40], // 过滤条件：只同步PMABENT=40的数据
            'batch_size' => env('SYNC_PMAB_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_PMAB_T_ENABLED', true),
        ],
        'PMAAL_T' => [
            'primary_key' => ['PMAALENT', 'PMAAL001', 'PMAAL002'], // 复合主键
            'timestamp_field' => null, // 无时间戳字段，使用全量同步
            'filter_conditions' => ['PMAALENT' => 40], // 过滤条件：只同步PMAALENT=40的数据
            'batch_size' => env('SYNC_PMAAL_T_BATCH_SIZE', 1000),
            'enabled' => env('SYNC_PMAAL_T_ENABLED', true),
        ],
    ],

    // 同步调度配置
    'schedule' => [
        // 增量同步频率（分钟）
        'incremental_frequency' => env('SYNC_INCREMENTAL_FREQUENCY', 5),
        
        // 全量同步时间（24小时制）
        'full_sync_time' => env('SYNC_FULL_SYNC_TIME', '02:00'),
        
        // 是否启用自动同步
        'auto_sync_enabled' => env('SYNC_AUTO_ENABLED', true),
        
        // 同步超时时间（秒）
        'timeout' => env('SYNC_TIMEOUT', 3600),
        
        // 最大重试次数
        'max_retries' => env('SYNC_MAX_RETRIES', 3),
    ],

    // 日志配置
    'logging' => [
        // 是否启用详细日志
        'verbose' => env('SYNC_VERBOSE_LOGGING', false),
        
        // 日志保留天数
        'retention_days' => env('SYNC_LOG_RETENTION_DAYS', 30),
        
        // 日志文件路径
        'log_file' => env('SYNC_LOG_FILE', 'sync.log'),
    ],

    // 性能配置
    'performance' => [
        // 默认批处理大小
        'default_batch_size' => env('SYNC_DEFAULT_BATCH_SIZE', 1000),
        
        // 内存限制（MB）
        'memory_limit' => env('SYNC_MEMORY_LIMIT', 512),
        
        // 是否启用并行处理
        'parallel_processing' => env('SYNC_PARALLEL_PROCESSING', false),
        
        // 并行处理的最大进程数
        'max_parallel_processes' => env('SYNC_MAX_PARALLEL_PROCESSES', 4),
    ],

    // 错误处理配置
    'error_handling' => [
        // 遇到错误时是否继续处理其他表
        'continue_on_error' => env('SYNC_CONTINUE_ON_ERROR', true),
        
        // 是否发送错误通知
        'send_error_notifications' => env('SYNC_SEND_ERROR_NOTIFICATIONS', false),
        
        // 错误通知邮箱
        'error_notification_email' => env('SYNC_ERROR_NOTIFICATION_EMAIL', null),
    ],

    // 监控配置
    'monitoring' => [
        // 是否启用性能监控
        'performance_monitoring' => env('SYNC_PERFORMANCE_MONITORING', true),
        
        // 慢查询阈值（秒）
        'slow_query_threshold' => env('SYNC_SLOW_QUERY_THRESHOLD', 10),
        
        // 是否记录查询统计
        'query_statistics' => env('SYNC_QUERY_STATISTICS', false),
    ],

    // 执行超时设置（秒）
    'execution' => [
        'max_execution_time' => 3600,  // 最大执行时间1小时
        'memory_limit' => '512M',      // 内存限制
        'batch_size' => 1000,          // 每批处理的记录数
    ],

    // 并发同步配置
    'parallel' => [
        'enabled' => true,             // 是否启用并发同步
        'max_workers' => 8,            // 最大并发任务数
        'timeout' => 3600,             // 任务超时时间（秒）
        'retry_attempts' => 3,         // 重试次数
    ],

    // 自动数据转化配置
    'auto_transform' => [
        'enabled' => env('SYNC_AUTO_TRANSFORM_ENABLED', true),    // 是否启用自动转化
        'company_code' => env('SYNC_AUTO_TRANSFORM_COMPANY', 'TB'), // 默认公司代码
        'locale' => env('SYNC_AUTO_TRANSFORM_LOCALE', 'zh_CN'),     // 默认语言
        'delay' => env('SYNC_AUTO_TRANSFORM_DELAY', 30),            // 转化延迟时间（秒）
        'queue' => env('SYNC_AUTO_TRANSFORM_QUEUE', 'transform'),   // 专用队列名称
        'timeout' => env('SYNC_AUTO_TRANSFORM_TIMEOUT', 3600),      // 转化任务超时（秒）
        'retry_attempts' => env('SYNC_AUTO_TRANSFORM_RETRIES', 3),  // 重试次数
        'incremental_mode' => true, // 启用增量模式
        
        // 不同转化类型的延迟配置（秒）
        'type_delays' => [
            'category' => 30,   // 分类转化：单表，延迟短
            'bom' => 30,        // BOM转化：单表，延迟短
            'customer' => 120,   // 客户转化：PMAB_T + PMAAL_T，延迟长确保两表都同步
            'material' => 300,  // 物料转化：IMAA_T + IMAAL_T + IMAF_T + BMAA_T，延迟最长
        ],
        
        // 表与转化类型映射关系
        'table_mapping' => [
            'RTAXL_T' => ['category'],     // 分类数据
            'PMAB_T' => ['customer'],      // 客户主表
            'PMAAL_T' => ['customer'],     // 客户多语言表
            'BMBA_T' => ['bom'],           // BOM表
            'IMAA_T' => ['material'],      // 物料主表
            'IMAAL_T' => ['material'],     // 物料多语言表
            'IMAF_T' => ['material'],      // 物料工厂表
            'BMAA_T' => ['material'],      // 物料BOM主表
        ],
        
        // 转化条件控制
        'conditions' => [
            'min_records_processed' => env('SYNC_AUTO_TRANSFORM_MIN_RECORDS', 0), // 最小处理记录数才触发转化
            'only_on_changes' => env('SYNC_AUTO_TRANSFORM_ONLY_CHANGES', false),  // 是否仅在有变更时触发
            'skip_empty_sync' => env('SYNC_AUTO_TRANSFORM_SKIP_EMPTY', true),     // 跳过空同步结果
            'deduplication' => env('SYNC_AUTO_TRANSFORM_DEDUPLICATION', true),    // 是否启用去重（避免重复排队）
            'dedup_window' => env('SYNC_AUTO_TRANSFORM_DEDUP_WINDOW', 600),       // 去重时间窗口（秒）
        ],
        
        // 多表关联配置
        'related_tables' => [
            'customer' => ['PMAB_T', 'PMAAL_T'],                            // 客户转化依赖的表
            'material' => ['IMAA_T', 'IMAAL_T', 'IMAF_T', 'BMAA_T'],       // 物料转化依赖的表
        ],
        
        // 通知配置
        'notifications' => [
            'enabled' => env('SYNC_AUTO_TRANSFORM_NOTIFICATIONS', false),     // 是否启用通知
            'success_notification' => env('SYNC_TRANSFORM_SUCCESS_NOTIFY', false), // 成功通知
            'failure_notification' => env('SYNC_TRANSFORM_FAILURE_NOTIFY', true),  // 失败通知
            'email' => env('SYNC_TRANSFORM_NOTIFICATION_EMAIL', null),             // 通知邮箱
        ],
    ],
]; 