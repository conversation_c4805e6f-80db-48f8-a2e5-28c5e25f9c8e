<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\SyncLog;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 全面同步数据差异分析 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

// 需要检查的表
$tables = ['BMAA_T', 'BMBA_T', 'IMAA_T', 'IMAAL_T', 'IMAF_T', 'PMAB_T', 'PMAAL_T'];

$totalOracleMissing = 0;
$totalMysqlRecords = 0;

foreach ($tables as $tableName) {
    echo "🔍 分析表: {$tableName}" . PHP_EOL;
    echo str_repeat('-', 50) . PHP_EOL;
    
    try {
        // 获取Oracle记录数（BMBAENT=40）
        $oracleCount = DB::connection('oracle')
            ->table($tableName)
            ->where(substr($tableName, 0, 4) . 'ENT', 40)
            ->count();
        
        // 获取MySQL记录数
        $mysqlCount = DB::table($tableName)->count();
        
        // 计算差异
        $difference = $oracleCount - $mysqlCount;
        $totalOracleMissing += $oracleCount;
        $totalMysqlRecords += $mysqlCount;
        
        echo "Oracle记录数 (ENT=40): " . number_format($oracleCount) . PHP_EOL;
        echo "MySQL记录数: " . number_format($mysqlCount) . PHP_EOL;
        echo "差异: " . number_format($difference) . " 条";
        
        if ($difference > 0) {
            $percentage = round(($difference / $oracleCount) * 100, 2);
            echo " ({$percentage}% 缺失)" . PHP_EOL;
        } else {
            echo " (正常)" . PHP_EOL;
        }
        
        // 获取最新同步日志
        $latestSync = SyncLog::where('table_name', $tableName)
            ->orderBy('created_at', 'desc')
            ->first();
        
        if ($latestSync) {
            echo "最新同步: " . $latestSync->created_at->format('Y-m-d H:i:s') . 
                 " (状态: {$latestSync->status})" . PHP_EOL;
            echo "同步处理: " . number_format($latestSync->records_processed ?? 0) . " 条" . PHP_EOL;
            echo "插入: " . number_format($latestSync->records_inserted ?? 0) . 
                 ", 更新: " . number_format($latestSync->records_updated ?? 0) . PHP_EOL;
            
            // 检查同步记录数与实际差异
            $syncTotal = ($latestSync->records_inserted ?? 0) + ($latestSync->records_updated ?? 0);
            if ($syncTotal != $mysqlCount && $latestSync->status === 'success') {
                echo "⚠️  警告: 同步记录数({$syncTotal})与MySQL实际记录数({$mysqlCount})不符!" . PHP_EOL;
            }
        } else {
            echo "❌ 未找到同步日志" . PHP_EOL;
        }
        
        // 检查数据分布（前3个前缀）
        $entField = substr($tableName, 0, 4) . 'ENT';
        $codeField = substr($tableName, 0, 4) . '001';
        
        echo "数据分布检查:" . PHP_EOL;
        
        // Oracle分布
        $oracleDistribution = DB::connection('oracle')
            ->table($tableName)
            ->where($entField, 40)
            ->selectRaw("SUBSTR({$codeField}, 1, 4) as prefix, COUNT(*) as count")
            ->groupBy(DB::raw("SUBSTR({$codeField}, 1, 4)"))
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get();
        
        // MySQL分布
        $mysqlDistribution = DB::table($tableName)
            ->selectRaw("SUBSTR({$codeField}, 1, 4) as prefix, COUNT(*) as count")
            ->groupBy(DB::raw("SUBSTR({$codeField}, 1, 4)"))
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get();
        
        $mysqlMap = $mysqlDistribution->keyBy('prefix');
        
        foreach ($oracleDistribution as $item) {
            $prefix = $item->prefix;
            $oracleCount = $item->count;
            $mysqlCount = isset($mysqlMap[$prefix]) ? $mysqlMap[$prefix]->count : 0;
            $diff = $oracleCount - $mysqlCount;
            $status = $diff == 0 ? '✅' : '❌';
            echo "  {$status} {$prefix}*: Oracle={$oracleCount}, MySQL={$mysqlCount}, 差异={$diff}" . PHP_EOL;
        }
        
    } catch (Exception $e) {
        echo "❌ 分析失败: " . $e->getMessage() . PHP_EOL;
    }
    
    echo PHP_EOL;
}

// 总体统计
echo "📊 总体统计:" . PHP_EOL;
echo str_repeat('=', 50) . PHP_EOL;
echo "Oracle总记录数: " . number_format($totalOracleMissing) . PHP_EOL;
echo "MySQL总记录数: " . number_format($totalMysqlRecords) . PHP_EOL;
echo "总缺失记录数: " . number_format($totalOracleMissing - $totalMysqlRecords) . PHP_EOL;
$totalPercentage = round((($totalOracleMissing - $totalMysqlRecords) / $totalOracleMissing) * 100, 2);
echo "总缺失比例: {$totalPercentage}%" . PHP_EOL . PHP_EOL;

// 分析可能的原因
echo "🔧 可能的原因分析:" . PHP_EOL;
echo "1. 同步过程中的静默失败 - 某些记录插入失败但未抛出异常" . PHP_EOL;
echo "2. 事务回滚 - 某些批次的事务被回滚但未记录" . PHP_EOL;
echo "3. 字符集问题 - 特殊字符导致插入失败" . PHP_EOL;
echo "4. 数据类型转换问题 - Oracle到MySQL的类型转换失败" . PHP_EOL;
echo "5. MySQL表约束 - 字段长度、唯一约束等导致插入失败" . PHP_EOL;
echo "6. 内存限制 - 大批量处理时内存不足导致部分数据丢失" . PHP_EOL;
echo "7. 连接超时 - 长时间同步过程中连接断开" . PHP_EOL . PHP_EOL;

echo "🎯 建议的解决方案:" . PHP_EOL;
echo "1. 启用详细错误日志记录" . PHP_EOL;
echo "2. 减小批次大小（从1000改为100）" . PHP_EOL;
echo "3. 增加失败记录统计和重试机制" . PHP_EOL;
echo "4. 使用事务日志验证数据完整性" . PHP_EOL;
echo "5. 添加数据验证和清理步骤" . PHP_EOL;

echo PHP_EOL . "=== 分析完成 ===" . PHP_EOL;
