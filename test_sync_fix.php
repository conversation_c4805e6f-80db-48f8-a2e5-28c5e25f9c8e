<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Services\DataSyncService;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试同步修复效果 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

// 选择一个较小的表进行测试
$testTable = 'PMAB_T';

echo "🧪 测试表: {$testTable}" . PHP_EOL;
echo str_repeat('-', 50) . PHP_EOL;

try {
    // 获取当前状态
    $oracleCount = DB::connection('oracle')->table($testTable)->where('PMABENT', 40)->count();
    $mysqlCountBefore = DB::table($testTable)->count();
    
    echo "测试前状态:" . PHP_EOL;
    echo "  Oracle记录数: " . number_format($oracleCount) . PHP_EOL;
    echo "  MySQL记录数: " . number_format($mysqlCountBefore) . PHP_EOL;
    echo "  差异: " . number_format($oracleCount - $mysqlCountBefore) . " 条" . PHP_EOL . PHP_EOL;
    
    // 清空MySQL表以进行完整测试
    echo "🗑️  清空MySQL表进行完整测试..." . PHP_EOL;
    DB::table($testTable)->truncate();
    echo "✅ 表已清空" . PHP_EOL . PHP_EOL;
    
    // 执行同步
    echo "🔄 开始同步测试..." . PHP_EOL;
    $syncService = app(DataSyncService::class);
    $result = $syncService->syncTable($testTable, 'full');
    
    echo "同步结果:" . PHP_EOL;
    echo "  成功: " . ($result['success'] ? '是' : '否') . PHP_EOL;
    echo "  处理记录数: " . number_format($result['records_processed'] ?? 0) . PHP_EOL;
    echo "  插入记录数: " . number_format($result['records_inserted'] ?? 0) . PHP_EOL;
    echo "  更新记录数: " . number_format($result['records_updated'] ?? 0) . PHP_EOL;
    echo "  失败记录数: " . number_format($result['records_failed'] ?? 0) . PHP_EOL;
    echo "  消息: " . ($result['message'] ?? '无') . PHP_EOL;
    
    if (isset($result['details']['success_rate'])) {
        echo "  成功率: " . $result['details']['success_rate'] . "%" . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // 验证结果
    $mysqlCountAfter = DB::table($testTable)->count();
    $expectedCount = ($result['records_inserted'] ?? 0) + ($result['records_updated'] ?? 0);
    
    echo "验证结果:" . PHP_EOL;
    echo "  MySQL实际记录数: " . number_format($mysqlCountAfter) . PHP_EOL;
    echo "  预期记录数: " . number_format($expectedCount) . PHP_EOL;
    echo "  数据一致性: " . ($mysqlCountAfter == $expectedCount ? '✅ 一致' : '❌ 不一致') . PHP_EOL;
    
    // 计算修复效果
    $beforeDifference = $oracleCount - $mysqlCountBefore;
    $afterDifference = $oracleCount - $mysqlCountAfter;
    $improvement = $beforeDifference - $afterDifference;
    
    echo PHP_EOL . "修复效果:" . PHP_EOL;
    echo "  修复前差异: " . number_format($beforeDifference) . " 条" . PHP_EOL;
    echo "  修复后差异: " . number_format($afterDifference) . " 条" . PHP_EOL;
    echo "  改善程度: " . number_format($improvement) . " 条" . PHP_EOL;
    
    if ($improvement > 0) {
        $improvementRate = round(($improvement / $beforeDifference) * 100, 2);
        echo "  改善率: {$improvementRate}%" . PHP_EOL;
    }
    
    // 检查失败记录详情
    if (($result['records_failed'] ?? 0) > 0) {
        echo PHP_EOL . "⚠️  失败记录分析:" . PHP_EOL;
        echo "  失败记录数: " . $result['records_failed'] . PHP_EOL;
        echo "  失败率: " . round(($result['records_failed'] / $oracleCount) * 100, 2) . "%" . PHP_EOL;
        echo "  建议: 检查应用日志查看失败原因" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . PHP_EOL;
    echo "错误详情: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== 测试完成 ===" . PHP_EOL;
