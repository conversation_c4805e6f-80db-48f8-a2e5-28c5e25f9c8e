<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== BMBA_T 数据同步修复工具 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 获取当前状态
    $oracleCount = DB::connection('oracle')->table('BMBA_T')->where('BMBAENT', 40)->count();
    $mysqlCount = DB::table('BMBA_T')->count();
    $difference = $oracleCount - $mysqlCount;
    
    echo "当前状态:" . PHP_EOL;
    echo "  Oracle记录数: " . number_format($oracleCount) . PHP_EOL;
    echo "  MySQL记录数: " . number_format($mysqlCount) . PHP_EOL;
    echo "  缺失记录数: " . number_format($difference) . PHP_EOL . PHP_EOL;
    
    if ($difference <= 0) {
        echo "✅ 数据已同步，无需修复" . PHP_EOL;
        exit(0);
    }
    
    // 2. 找出缺失的记录
    echo "🔍 查找缺失的记录..." . PHP_EOL;
    
    // 分批查找缺失记录
    $batchSize = 1000;
    $offset = 0;
    $missingRecords = [];
    $totalChecked = 0;
    
    while ($offset < $oracleCount && count($missingRecords) < 100) { // 限制只找前100条缺失记录作为样本
        $oracleRecords = DB::connection('oracle')
            ->table('BMBA_T')
            ->where('BMBAENT', 40)
            ->offset($offset)
            ->limit($batchSize)
            ->get(['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004']);
        
        foreach ($oracleRecords as $record) {
            $exists = DB::table('BMBA_T')
                ->where('BMBAENT', $record->bmbaent ?? $record->BMBAENT)
                ->where('BMBASITE', $record->bmbasite ?? $record->BMBASITE)
                ->where('BMBA001', $record->bmba001 ?? $record->BMBA001)
                ->where('BMBA003', $record->bmba003 ?? $record->BMBA003)
                ->where('BMBA004', $record->bmba004 ?? $record->BMBA004)
                ->exists();

            if (!$exists) {
                $missingRecords[] = [
                    'BMBAENT' => $record->bmbaent ?? $record->BMBAENT,
                    'BMBASITE' => $record->bmbasite ?? $record->BMBASITE,
                    'BMBA001' => $record->bmba001 ?? $record->BMBA001,
                    'BMBA003' => $record->bmba003 ?? $record->BMBA003,
                    'BMBA004' => $record->bmba004 ?? $record->BMBA004,
                ];
                if (count($missingRecords) >= 100) break;
            }

            $totalChecked++;
        }
        
        $offset += $batchSize;
        
        // 显示进度
        $progress = min(100, ($totalChecked / $oracleCount) * 100);
        echo "  检查进度: " . number_format($totalChecked) . "/" . number_format($oracleCount) . " (" . round($progress, 1) . "%), 发现缺失: " . count($missingRecords) . " 条\r";
        
        if (empty($oracleRecords) || $oracleRecords->count() < $batchSize) {
            break;
        }
    }
    
    echo PHP_EOL . "✅ 检查完成，发现缺失记录样本: " . count($missingRecords) . " 条" . PHP_EOL . PHP_EOL;
    
    // 3. 分析缺失记录的特征
    if (!empty($missingRecords)) {
        echo "📊 缺失记录分析:" . PHP_EOL;
        
        // 显示前5条缺失记录
        echo "  前5条缺失记录:" . PHP_EOL;
        for ($i = 0; $i < min(5, count($missingRecords)); $i++) {
            $record = $missingRecords[$i];
            echo "    " . ($i+1) . ". BMBA001={$record['BMBA001']}, BMBA003={$record['BMBA003']}, BMBA004={$record['BMBA004']}" . PHP_EOL;
        }
        echo PHP_EOL;
        
        // 4. 尝试手动同步第一条缺失记录
        echo "🔧 尝试手动同步第一条缺失记录..." . PHP_EOL;
        $testRecord = $missingRecords[0];
        
        try {
            // 从Oracle获取完整记录
            $fullRecord = DB::connection('oracle')
                ->table('BMBA_T')
                ->where('BMBAENT', $testRecord['BMBAENT'])
                ->where('BMBASITE', $testRecord['BMBASITE'])
                ->where('BMBA001', $testRecord['BMBA001'])
                ->where('BMBA003', $testRecord['BMBA003'])
                ->where('BMBA004', $testRecord['BMBA004'])
                ->first();
            
            if ($fullRecord) {
                $recordArray = (array) $fullRecord;
                
                // 转换字段名为大写
                $upperCaseRecord = [];
                foreach ($recordArray as $key => $value) {
                    $upperCaseRecord[strtoupper($key)] = $value;
                }
                
                // 尝试插入
                DB::table('BMBA_T')->insert($upperCaseRecord);
                echo "  ✅ 成功插入测试记录" . PHP_EOL;
                
                // 验证插入
                $inserted = DB::table('BMBA_T')
                    ->where('BMBAENT', $testRecord['BMBAENT'])
                    ->where('BMBASITE', $testRecord['BMBASITE'])
                    ->where('BMBA001', $testRecord['BMBA001'])
                    ->where('BMBA003', $testRecord['BMBA003'])
                    ->where('BMBA004', $testRecord['BMBA004'])
                    ->exists();
                
                if ($inserted) {
                    echo "  ✅ 验证成功：记录已存在于MySQL中" . PHP_EOL;
                } else {
                    echo "  ❌ 验证失败：记录插入后仍不存在" . PHP_EOL;
                }
            }
            
        } catch (Exception $e) {
            echo "  ❌ 插入失败: " . $e->getMessage() . PHP_EOL;
            echo "  错误详情: " . PHP_EOL;
            
            // 分析错误原因
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "    - 主键冲突：记录可能已存在但查询条件有问题" . PHP_EOL;
            } elseif (strpos($e->getMessage(), 'Data too long') !== false) {
                echo "    - 数据长度超限：某些字段值超过MySQL字段长度限制" . PHP_EOL;
            } elseif (strpos($e->getMessage(), 'Incorrect') !== false) {
                echo "    - 数据类型错误：Oracle和MySQL数据类型不匹配" . PHP_EOL;
            } else {
                echo "    - 其他错误：" . $e->getMessage() . PHP_EOL;
            }
        }
    }
    
    echo PHP_EOL;
    
    // 5. 建议修复方案
    echo "🎯 修复建议:" . PHP_EOL;
    echo "1. 立即方案：" . PHP_EOL;
    echo "   - 重新执行全量同步: php artisan oracle:sync --table=BMBA_T --type=full --force" . PHP_EOL;
    echo "   - 启用详细日志监控失败记录" . PHP_EOL;
    echo PHP_EOL;
    
    echo "2. 长期方案：" . PHP_EOL;
    echo "   - 改进同步代码，增加失败记录统计" . PHP_EOL;
    echo "   - 添加数据验证和错误处理机制" . PHP_EOL;
    echo "   - 使用更小的批次大小减少事务风险" . PHP_EOL;
    echo PHP_EOL;
    
    echo "3. 监控方案：" . PHP_EOL;
    echo "   - 定期运行此诊断脚本检查数据一致性" . PHP_EOL;
    echo "   - 设置数据差异告警" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ 修复过程中发生错误: " . $e->getMessage() . PHP_EOL;
    echo "错误详情: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== 修复工具执行完成 ===" . PHP_EOL;
